/**
 * gRPC 相关处理器
 */
import { join, dirname } from 'node:path'
import { readFileSync, existsSync } from 'node:fs'
import { fileURLToPath } from 'node:url'
import { logger, type LinkerGrpcClient, createLinkerClient } from '@mattverse/electron-core'

// 获取当前文件的路径信息
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// 全局 gRPC 客户端引用
let grpcClient: LinkerGrpcClient | null = null

/**
 * 获取配置的函数 - 优先使用 middle.config.json，不存在时使用环境变量
 */
function getMiddlewareConfig() {
  const configPath = join(__dirname, '..', '..', 'middle.config.json')

  // 检查 middle.config.json 文件是否存在
  if (existsSync(configPath)) {
    try {
      const configContent = readFileSync(configPath, 'utf-8')
      const configData = JSON.parse(configContent)
      logger.info('使用 middle.config.json 配置:', configData)
      return {
        host: configData.host || '',
        port: configData.port || null,
      }
    } catch (error) {
      logger.warn('读取 middle.config.json 失败，回退到环境变量:', error)
    }
  }

  // 回退到环境变量（从 .env.development 文件）
  const configData = {
    host: process.env.VITE_APP_LINKER_HOST || '',
    port: parseInt(process.env.VITE_APP_LINKER_PORT || '') || null,
  }
  logger.info('使用环境变量配置:', configData)
  return configData
}

/**
 * 初始化 gRPC 客户端
 */
function initGrpcClient() {
  try {
    const middlewareConfig = getMiddlewareConfig()
    const config = {
      host: middlewareConfig.host,
      port: middlewareConfig.port,
      secure: false,
    }

    const userId = process.env.VITE_APP_USER_ID || '0'
    const token = process.env.VITE_APP_USER_TOKEN || 'abcdefghijklmn'

    grpcClient = createLinkerClient(config, userId, token)

    logger.info('Mattverse gRPC 客户端初始化成功:', {
      host: config.host,
      port: config.port,
      userId,
      token: token,
    })

    return grpcClient
  } catch (error) {
    logger.error('Mattverse gRPC 客户端初始化失败:', error)
    return null
  }
}

/**
 * 设置 gRPC 客户端实例
 */
export function setGrpcClient(client: LinkerGrpcClient | null) {
  grpcClient = client
}

/**
 * 获取 gRPC 客户端实例
 */
export function getGrpcClient() {
  return grpcClient
}

/**
 * 初始化 gRPC 客户端（供外部调用）
 */
export function initializeGrpcClient() {
  return initGrpcClient()
}

/**
 * gRPC 相关的 IPC 处理器
 */
export const grpcHandlers = {
  // gRPC 初始化
  'grpc:init': async () => {
    try {
      if (!grpcClient) {
        grpcClient = initGrpcClient()
      }
      return {
        success: !!grpcClient,
        connected: grpcClient?.isConnected() || false,
        message: grpcClient ? 'gRPC 客户端已初始化' : 'gRPC 客户端初始化失败',
      }
    } catch (error) {
      logger.error('gRPC 初始化失败:', error)
      return {
        success: false,
        connected: false,
        error: error instanceof Error ? error.message : '初始化失败',
      }
    }
  },

  // gRPC Ping 测试
  'grpc:ping': async () => {
    return new Promise((resolve) => {
      if (!grpcClient) {
        resolve({
          success: false,
          error: 'gRPC 客户端未初始化',
        })
        return
      }

      grpcClient.ping((error, response) => {
        if (error) {
          logger.error('gRPC Ping 失败:', error)
          resolve({
            success: false,
            error: error.message,
          })
        } else {
          logger.info('gRPC Ping 成功:', response)
          resolve({
            success: true,
            data: response,
          })
        }
      })
    })
  },

  // gRPC 通用调用
  'grpc:call': async (apiName: string, params: Record<string, any>) => {
    return new Promise((resolve) => {
      if (!grpcClient) {
        resolve({
          success: false,
          error: 'gRPC 客户端未初始化',
        })
        return
      }

      grpcClient.call(apiName, params, (error, response) => {
        if (error) {
          logger.error(`gRPC 调用 ${apiName} 失败:`, error)
          resolve({
            success: false,
            error: error.message,
          })
        } else {
          logger.info(`gRPC 调用 ${apiName} 成功:`, response)
          resolve({
            success: true,
            data: response,
          })
        }
      })
    })
  },

  // 获取 gRPC 状态
  'grpc:get-status': async () => {
    return {
      initialized: !!grpcClient,
      connected: grpcClient?.isConnected() || false,
      url: grpcClient?.getLinkerUrl() || null,
      userId: grpcClient?.getUserId() || null,
    }
  },
}
