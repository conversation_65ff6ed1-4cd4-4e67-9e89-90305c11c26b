<template>
  <div class="flex flex-col h-[calc(100vh-3rem)] overflow-hidden">
    <!-- 页面标题和操作按钮 -->
    <div class="flex-shrink-0 px-6 py-4 border-0 bg-background">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold tracking-tight">{{ $t('settings.title') }}</h1>
          <p class="text-muted-foreground text-sm mt-1">
            {{ $t('settings.basic_desc') }}
          </p>
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-center space-x-2">
          <Button variant="outline" size="sm" @click="exportSettings">
            <MattIcon name="Download" class="mr-2 h-4 w-4" />
            {{ $t('settings.export_settings') }}
          </Button>
          <Button
            v-for="action in actionButtons"
            :key="action.key"
            :variant="action.variant"
            size="sm"
            @click="action.handler"
          >
            <MattIcon :name="action.icon" class="mr-2 h-4 w-4" />
            {{ action.label }}
          </Button>
        </div>
      </div>
    </div>

    <!-- 设置内容 - 可滚动区域 -->
    <div class="flex-1 min-h-0 overflow-y-auto overflow-x-hidden scrollbar">
      <div class="container mx-auto max-w-none p-8">
        <!-- 响应式Grid瀑布流布局 -->
        <div
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 auto-rows-max max-w-[1600px] mx-auto"
        >
          <!-- 外观设置 -->
          <Card
            class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
          >
            <CardHeader>
              <CardTitle class="flex items-center space-x-2">
                <MattIcon name="Palette" class="h-5 w-5" />
                <span>{{ $t('settings.appearance') }}</span>
              </CardTitle>
              <CardDescription>
                {{ $t('settings.appearance_desc') }}
              </CardDescription>
            </CardHeader>
            <CardContent class="space-y-6">
              <!-- 语言设置 -->
              <LanguageSelector />

              <!-- 主题设置 -->
              <Separator />
              <ThemeSelector />

              <!-- 字体设置 -->
              <Separator />
              <FontSelector />
            </CardContent>
          </Card>

          <!-- 缓存设置 -->
          <Card
            class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
          >
            <CardHeader>
              <CardTitle class="flex items-center space-x-2">
                <MattIcon name="Database" class="h-5 w-5" />
                <span>{{ $t('settings.cache') }}</span>
              </CardTitle>
              <CardDescription>
                {{ $t('settings.cache_desc') }}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CacheSettings />
            </CardContent>
          </Card>

          <!-- 通知设置 -->
          <Card
            class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
          >
            <CardHeader>
              <CardTitle class="flex items-center space-x-2">
                <MattIcon name="Bell" class="h-5 w-5" />
                <span>{{ $t('settings.notifications') }}</span>
              </CardTitle>
              <CardDescription>
                {{ $t('settings.notifications_desc') }}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <NotificationSettings />
            </CardContent>
          </Card>

          <!-- 高级设置 -->
          <Card
            class="break-inside-avoid md:col-span-2 lg:col-span-1 bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
          >
            <CardHeader>
              <CardTitle class="flex items-center space-x-2">
                <MattIcon name="Settings" class="h-5 w-5" />
                <span>{{ $t('settings.advanced') }}</span>
              </CardTitle>
              <CardDescription>
                {{ $t('settings.advanced_desc') }}
              </CardDescription>
            </CardHeader>
            <CardContent class="space-y-6">
              <!-- 自动保存 -->
              <div class="flex items-center justify-between">
                <div class="space-y-0.5">
                  <Label class="text-sm">{{ $t('settings.advanced_options.auto_save') }}</Label>
                  <p class="text-xs text-muted-foreground">
                    {{ $t('settings.advanced_options.auto_save_desc') }}
                  </p>
                </div>
                <Switch
                  :checked="advanced.autoSave"
                  @update:checked="updateAdvanced('autoSave', $event)"
                />
              </div>

              <Separator />

              <!-- 调试模式 -->
              <div class="flex items-center justify-between">
                <div class="space-y-0.5">
                  <Label class="text-sm">{{ $t('settings.advanced_options.debug_mode') }}</Label>
                  <p class="text-xs text-muted-foreground">
                    {{ $t('settings.advanced_options.debug_mode_desc') }}
                  </p>
                </div>
                <Switch
                  :checked="advanced.debugMode"
                  @update:checked="updateAdvanced('debugMode', $event)"
                />
              </div>

              <Separator />

              <!-- 备份频率 -->
              <div class="space-y-3">
                <Label class="text-sm">{{
                  $t('settings.advanced_options.backup_frequency')
                }}</Label>
                <Select
                  :model-value="advanced.backupFrequency"
                  @update:model-value="updateAdvanced('backupFrequency', $event)"
                >
                  <SelectTrigger class="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="option in backupOptions"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <!-- 缓存大小 -->
              <div class="space-y-3">
                <Label class="text-sm">{{ $t('settings.advanced_options.cache_size') }} (MB)</Label>
                <div class="flex items-center space-x-4">
                  <Slider
                    :model-value="[advanced.cacheSize]"
                    :max="500"
                    :min="50"
                    :step="10"
                    class="flex-1"
                    @update:model-value="updateAdvanced('cacheSize', $event[0])"
                  />
                  <span class="text-sm text-muted-foreground w-12">{{ advanced.cacheSize }}MB</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from '@mattverse/i18n'
import { useSettingsStore, type AdvancedSettings } from '@/store'
import {
  LanguageSelector,
  ThemeSelector,
  FontSelector,
  CacheSettings,
  NotificationSettings,
} from './components'

const { t } = useI18n()
const settingsStore = useSettingsStore()

const advanced = computed(() => settingsStore.advanced)

const backupOptions = computed(() => [
  { value: 'never', label: t('settings.backup_options.never') },
  { value: 'daily', label: t('settings.backup_options.daily') },
  { value: 'weekly', label: t('settings.backup_options.weekly') },
  { value: 'monthly', label: t('settings.backup_options.monthly') },
])

const actionButtons = computed(() => [
  {
    key: 'reset',
    variant: 'outline' as const,
    icon: 'RotateCcw',
    label: t('settings.reset_to_defaults'),
    handler: resetSettings,
  },
  {
    key: 'save',
    variant: 'default' as const,
    icon: 'Save',
    label: t('common.save'),
    handler: saveSettings,
  },
])

const updateAdvanced = (key: keyof AdvancedSettings, value: any) => {
  settingsStore.updateAdvanced({ [key]: value })
}

const exportSettings = () => {
  try {
    const settings = settingsStore.exportSettings()
    const dataStr = JSON.stringify(settings, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })

    const link = document.createElement('a')
    link.href = URL.createObjectURL(dataBlob)
    link.download = `mattverse-settings-${new Date().toISOString().split('T')[0]}.json`
    link.click()

    console.log(t('settings.export_success'))
  } catch {
    console.error(t('settings.export_failed'))
  }
}

const resetSettings = () => {
  settingsStore.resetToDefaults()
  console.log(t('settings.reset_success'))
}

const saveSettings = () => {
  // 设置会自动持久化，这里只是给用户反馈
  console.log(t('settings.save_success'))
}
</script>
