export interface MattSearchFormField {
  /** 字段名 */
  name: string
  /** 字段标签 */
  label?: string
  /** 字段类型 */
  type: 'input' | 'select' | 'date' | 'daterange' | 'number'
  /** 占位符 */
  placeholder?: string
  /** 选项列表（用于 select 类型） */
  options?: Array<{ label: string; value: any; icon?: string }>
  /** 默认值 */
  defaultValue?: any
  /** 是否必填 */
  required?: boolean
  /** 字段宽度 */
  width?: string
  /** 自定义样式类 */
  class?: string
  /** 字段图标 */
  icon?: string
  /** 是否显示标签 */
  showLabel?: boolean
}

export interface MattSearchFormProps {
  /** 搜索字段配置 */
  fields?: MattSearchFormField[]
  /** 是否显示搜索按钮 */
  showSearch?: boolean
  /** 是否显示重置按钮 */
  showReset?: boolean
  /** 是否显示时间排序 */
  showTimeSort?: boolean
  /** 是否显示状态筛选 */
  showStatusFilter?: boolean
  /** 状态选项 */
  statusOptions?: Array<{ label: string; value: any; icon?: string }>
  /** 是否显示筛选列表 */
  showFilterList?: boolean
  /** 筛选列表选项 */
  filterListOptions?: Array<{ label: string; value: any; icon?: string }>
  /** 是否显示列筛选 @deprecated 请使用 MattTable 的 columnFilterOptions */
  showColumnFilter?: boolean
  /** 列筛选配置 @deprecated 请使用 MattTable 的 columnFilterOptions */
  columnFilterOptions?: MattColumnFilterOption[]
  /** 是否可折叠 */
  collapsible?: boolean
  /** 默认是否展开 */
  defaultExpanded?: boolean
  /** 每行显示的字段数量 */
  fieldsPerRow?: number
  /** 表单样式类 */
  class?: string
  /** 加载状态 */
  loading?: boolean
  /** 是否显示标签 */
  showLabels?: boolean
  /** 表单风格 */
  variant?: 'default' | 'compact' | 'minimal'
  /** 是否显示边框 */
  bordered?: boolean
}

/** @deprecated 请使用 MattTable 中的 MattColumnFilterOption */
export interface MattColumnFilterOption {
  /** 列的唯一标识 */
  key: string
  /** 列标题 */
  label: string
  /** 是否默认显示 */
  defaultVisible?: boolean
  /** 是否可以隐藏（某些核心列可能不允许隐藏） */
  hideable?: boolean
}

export interface MattSearchFormEmits {
  'search': [values: Record<string, any>]
  'reset': []
  'time-sort-change': [order: 'asc' | 'desc' | null]
  'status-change': [status: any]
  'filter-change': [filter: any]
  /** @deprecated 请使用 MattTable 的 column-change 事件 */
  'column-change': [visibleColumns: string[]]
}

export interface MattSearchFormValues {
  [key: string]: any
  timeSort?: 'asc' | 'desc' | null
  status?: any
  filter?: any
  /** @deprecated 请使用 MattTable 的 visibleColumns 属性 */
  visibleColumns?: string[]
}
